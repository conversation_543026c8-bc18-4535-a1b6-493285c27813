import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// GET handler to retrieve user goals
const getGoals = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }

  const { searchParams } = new URL(request.url);
  const status = searchParams.get('status');
  const type = searchParams.get('type');
  const category = searchParams.get('category');

  const whereClause: any = {
    userId: session.user.id,
  };

  if (status) {
    whereClause.status = status;
  }

  if (type) {
    whereClause.type = type;
  }

  if (category) {
    whereClause.category = category;
  }

  const goals = await prisma.userGoal.findMany({
    where: whereClause,
    orderBy: [
      { status: 'asc' }, // Active goals first
      { createdAt: 'desc' },
    ],
  });

  // Calculate goal statistics
  const stats = {
    total: goals.length,
    active: goals.filter(g => g.status === 'ACTIVE').length,
    completed: goals.filter(g => g.status === 'COMPLETED').length,
    paused: goals.filter(g => g.status === 'PAUSED').length,
    cancelled: goals.filter(g => g.status === 'CANCELLED').length,
  };

  return NextResponse.json({
    success: true,
    data: {
      goals,
      stats,
    }
  });
});

export const GET = getGoals;

// POST handler to create a new goal
const createGoal = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 goal creations per 15 minutes
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          throw new Error('Unauthorized');
        }

        const body = await request.json();
        const {
          title,
          description,
          type,
          category,
          targetValue,
          targetDate,
          isPublic,
        } = body;

        // Validation
        if (!title || !type || !category || !targetValue) {
          throw new Error('Title, type, category, and target value are required');
        }

        if (targetValue <= 0) {
          throw new Error('Target value must be greater than 0');
        }

        // Validate enums
        const validTypes = ['DAILY', 'WEEKLY', 'MONTHLY', 'YEARLY', 'CUSTOM'];
        const validCategories = [
          'LEARNING_RESOURCES',
          'SKILLS',
          'CERTIFICATIONS',
          'PROJECTS',
          'CAREER_MILESTONES',
          'NETWORKING',
        ];

        if (!validTypes.includes(type)) {
          throw new Error('Invalid goal type');
        }

        if (!validCategories.includes(category)) {
          throw new Error('Invalid goal category');
        }

        const goal = await prisma.userGoal.create({
          data: {
            userId: session.user.id,
            title: title.trim(),
            description: description?.trim(),
            type,
            category,
            targetValue,
            targetDate: targetDate ? new Date(targetDate) : null,
            isPublic: isPublic || false,
          },
        });

        return NextResponse.json({
          success: true,
          data: goal
        }, { status: 201 });
      }
    );
  });
});

export const POST = createGoal;

// PUT handler to update a goal
const updateGoal = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 50 }, // 50 goal updates per 15 minutes
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          throw new Error('Unauthorized');
        }

        const body = await request.json();
        const {
          id,
          title,
          description,
          targetValue,
          currentValue,
          targetDate,
          status,
          isPublic,
        } = body;

        if (!id) {
          throw new Error('Goal ID is required');
        }

        // Check if goal exists and belongs to user
        const existingGoal = await prisma.userGoal.findFirst({
          where: {
            id,
            userId: session.user.id,
          },
        });

        if (!existingGoal) {
          throw new Error('Goal not found');
        }

        const updateData: any = {};

        if (title !== undefined) updateData.title = title.trim();
        if (description !== undefined) updateData.description = description?.trim();
        if (targetValue !== undefined) updateData.targetValue = targetValue;
        if (currentValue !== undefined) updateData.currentValue = currentValue;
        if (targetDate !== undefined) updateData.targetDate = targetDate ? new Date(targetDate) : null;
        if (status !== undefined) updateData.status = status;
        if (isPublic !== undefined) updateData.isPublic = isPublic;

        // If marking as completed, set completedAt
        if (status === 'COMPLETED' && existingGoal.status !== 'COMPLETED') {
          updateData.completedAt = new Date();
        }

        // If changing from completed to another status, clear completedAt
        if (status !== 'COMPLETED' && existingGoal.status === 'COMPLETED') {
          updateData.completedAt = null;
        }

        const updatedGoal = await prisma.userGoal.update({
          where: { id },
          data: updateData,
        });

        return NextResponse.json({
          success: true,
          data: updatedGoal
        });
      }
    );
  });
});

export const PUT = updateGoal;

// DELETE handler to delete a goal
const deleteGoal = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 goal deletions per 15 minutes
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          throw new Error('Unauthorized');
        }

        const { searchParams } = new URL(request.url);
        const id = searchParams.get('id');

        if (!id) {
          throw new Error('Goal ID is required');
        }

        // Check if goal exists and belongs to user
        const goal = await prisma.userGoal.findFirst({
          where: {
            id,
            userId: session.user.id,
          },
        });

        if (!goal) {
          throw new Error('Goal not found');
        }

        await prisma.userGoal.delete({
          where: { id },
        });

        return NextResponse.json({
          success: true,
          data: { message: 'Goal deleted successfully' }
        });
      }
    );
  });
});

export const DELETE = deleteGoal;
