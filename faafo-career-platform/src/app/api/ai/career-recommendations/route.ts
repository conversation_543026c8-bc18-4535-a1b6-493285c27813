import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { geminiService } from '@/lib/services/geminiService';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { prisma } from '@/lib/prisma';
import { optimizedAIService } from '@/lib/optimized-ai-service';
import { z } from 'zod';

// TypeScript interfaces
interface CareerRecommendationsResponse {
  recommendations: any[];
  metadata: {
    generatedAt: string;
    basedOnAssessment: boolean;
    assessmentId?: string;
    skillsCount: number;
    hasPreferences: boolean;
  };
}

interface CachedRecommendationsResponse {
  data: CareerRecommendationsResponse;
  cached: boolean;
  message?: string;
}

interface ClearCacheResponse {
  message: string;
}

// Validation schema
const careerRecommendationsSchema = z.object({
  assessmentId: z.string().uuid().optional(),
  currentSkills: z.array(z.string()).min(1, 'At least one skill is required').max(50, 'Too many skills'),
  preferences: z.object({
    workEnvironment: z.enum(['remote', 'hybrid', 'office', 'flexible']).optional(),
    industryPreferences: z.array(z.string()).optional(),
    salaryExpectations: z.object({
      min: z.number().min(0).optional(),
      max: z.number().min(0).optional(),
    }).optional(),
    workLifeBalance: z.enum(['high', 'medium', 'low']).optional(),
    riskTolerance: z.enum(['high', 'medium', 'low']).optional(),
    careerStage: z.enum(['entry', 'mid', 'senior', 'executive']).optional(),
  }).optional(),
  includeAssessmentData: z.boolean().optional().default(true),
});

async function getAssessmentData(userId: string, assessmentId?: string) {
  let assessment;

  if (assessmentId) {
    assessment = await prisma.assessment.findFirst({
      where: {
        id: assessmentId,
        userId: userId,
        status: 'COMPLETED'
      },
      include: {
        responses: true
      }
    });
  } else {
    // Get the most recent completed assessment
    assessment = await prisma.assessment.findFirst({
      where: {
        userId: userId,
        status: 'COMPLETED'
      },
      include: {
        responses: true
      },
      orderBy: {
        completedAt: 'desc'
      }
    });
  }

  if (!assessment) {
    return null;
  }

  // Transform responses into a more usable format
  const assessmentData = {
    id: assessment.id,
    completedAt: assessment.completedAt,
    responses: assessment.responses.reduce((acc, response) => {
      acc[response.questionKey] = response.answerValue;
      return acc;
    }, {} as Record<string, any>)
  };

  return assessmentData;
}

async function handleCareerRecommendations(request: NextRequest): Promise<NextResponse<ApiResponse<CareerRecommendationsResponse>>> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;

  const body = await request.json();
  const validation = careerRecommendationsSchema.safeParse(body);

  if (!validation.success) {
    const error = new Error('Invalid request data') as any;
    error.statusCode = 400;
    error.details = validation.error.errors;
    throw error;
  }

  const { assessmentId, currentSkills, preferences, includeAssessmentData } = validation.data;

  // Get assessment data if requested
  let assessmentData = null;
  if (includeAssessmentData) {
    assessmentData = await getAssessmentData(userId, assessmentId);
  }

  // Generate cache key based on input parameters
  const cacheKeyParams = [
    assessmentId || 'no-assessment',
    currentSkills.sort().join(','),
    JSON.stringify(preferences || {}),
    includeAssessmentData.toString()
  ];
  const cacheKey = `ai:career_recommendations:${userId}:${cacheKeyParams.join('|')}`;

  // Check cache first
  const cached = await consolidatedCache.get<any>(cacheKey);
  if (cached) {
    return NextResponse.json({
      success: true,
      data: cached as CareerRecommendationsResponse,
      cached: true,
      message: 'Career recommendations retrieved from cache'
    });
  }

  // Prepare data for AI analysis
  const analysisData = {
    assessmentData: assessmentData?.responses || {},
    assessmentId: assessmentData?.id,
    assessmentCompletedAt: assessmentData?.completedAt,
    currentSkills,
    preferences: preferences || {},
    userId // For context but not for AI processing
  };

  // Generate AI recommendations using optimized service
  const recommendationsResult = await optimizedAIService.generateCareerRecommendations(
    currentSkills,
    preferences || {},
    {
      userId,
      enableDeduplication: true,
      enableSemanticMatch: true,
      priority: 'medium'
    }
  );

  if (!recommendationsResult.success) {
    const error = new Error(recommendationsResult.error || 'Failed to generate career recommendations') as any;
    error.statusCode = 500;
    throw error;
  }

  // Enhance recommendations with additional data
  const enhancedData = {
    ...recommendationsResult.data,
    metadata: {
      generatedAt: new Date().toISOString(),
      basedOnAssessment: !!assessmentData,
      assessmentId: assessmentData?.id,
      skillsCount: currentSkills.length,
      hasPreferences: !!preferences && Object.keys(preferences).length > 0
    }
  };

  // Cache the result for 6 hours (recommendations can change based on market conditions)
  await consolidatedCache.set(cacheKey, enhancedData, { ttl: 6 * 60 * 60 * 1000, tags: ['career_recommendations', userId] });

  // Track usage analytics
  console.log(`Career recommendations generated for user ${userId}, assessment: ${assessmentId || 'none'}`);

  return NextResponse.json({
    success: true,
    data: enhancedData,
    cached: false,
    message: 'Career recommendations generated successfully'
  });
}

// GET endpoint for retrieving user's recent recommendations
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<CachedRecommendationsResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 30 }, // 30 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const assessmentId = searchParams.get('assessmentId');

      // Get user's recent assessment if no specific ID provided
      let targetAssessmentId = assessmentId;
      if (!targetAssessmentId) {
        const recentAssessment = await prisma.assessment.findFirst({
          where: {
            userId: userId,
            status: 'COMPLETED'
          },
          orderBy: {
            completedAt: 'desc'
          },
          select: {
            id: true
          }
        });
        targetAssessmentId = recentAssessment?.id || 'no-assessment';
      }

      // Try to find cached recommendations
      // Note: This is a simplified approach - in production you might want to store cache keys
      const possibleCacheKey = `ai:career_recommendations:${userId}:${targetAssessmentId}`;
      const cached = await consolidatedCache.get<any>(possibleCacheKey);

      if (cached) {
        return NextResponse.json({
          success: true,
          data: cached,
          cached: true
        });
      }

      const error = new Error('No cached recommendations found. Please generate new recommendations.') as any;
      error.statusCode = 404;
      error.code = 'NO_CACHE';
      throw error;
    }
  ) as Promise<NextResponse<ApiResponse<CachedRecommendationsResponse>>>;
});

// POST endpoint for generating new recommendations
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<CareerRecommendationsResponse>>> => {
  return withCSRFProtection(request, async (): Promise<NextResponse<ApiResponse<CareerRecommendationsResponse>>> => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 generations per 15 minutes
      () => handleCareerRecommendations(request)
    ) as Promise<NextResponse<ApiResponse<CareerRecommendationsResponse>>>;
  }) as Promise<NextResponse<ApiResponse<CareerRecommendationsResponse>>>;
});

// DELETE endpoint for clearing cached recommendations
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ClearCacheResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 15 }, // 15 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const assessmentId = searchParams.get('assessmentId');

      // Clear specific or all recommendations cache for user
      if (assessmentId) {
        const cacheKey = `ai:career_recommendations:${userId}:${assessmentId}`;
        await consolidatedCache.delete(cacheKey);
      } else {
        // Clear all recommendation caches for user using tag-based invalidation
        await consolidatedCache.invalidateByTags(['career_recommendations', userId]);
      }

      return NextResponse.json({
        success: true,
        data: { message: 'Recommendation cache cleared' }
      });
    }
  ) as Promise<NextResponse<ApiResponse<ClearCacheResponse>>>;
});
