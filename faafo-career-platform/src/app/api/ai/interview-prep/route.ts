import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { geminiService } from '@/lib/services/geminiService';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';
import { withCSRFProtection } from '@/lib/csrf';
import { optimizedAIService } from '@/lib/optimized-ai-service';

// TypeScript interfaces
interface InterviewPrepResponse {
  data: any;
  metadata: {
    generatedAt: string;
    careerPath: string;
    experienceLevel: string;
    companyType: string;
    industryFocus?: string;
    specificRole?: string;
    interviewType: string;
    preparationTime: string;
    focusAreas: string[];
  };
  preparationPlan: any;
  companyResearch: any;
  followUpQuestions: any;
  redFlags: any;
  salaryNegotiation: any;
  cached?: boolean;
  message?: string;
}

// Validation schema
const interviewPrepSchema = z.object({
  careerPath: z.string().min(2, 'Career path is required').max(200, 'Career path name too long'),
  experienceLevel: z.enum(['entry', 'mid', 'senior', 'executive']),
  companyType: z.enum(['startup', 'corporate', 'nonprofit', 'government', 'consulting', 'agency', 'freelance']),
  industryFocus: z.string().optional(),
  specificRole: z.string().optional(),
  interviewType: z.enum(['phone', 'video', 'in_person', 'panel', 'technical', 'behavioral']).optional().default('video'),
  preparationTime: z.enum(['1_day', '3_days', '1_week', '2_weeks']).optional().default('1_week'),
  focusAreas: z.array(z.enum(['technical', 'behavioral', 'cultural_fit', 'leadership', 'problem_solving'])).optional().default(['technical', 'behavioral']),
});

async function handleInterviewPrep(request: NextRequest): Promise<NextResponse<ApiResponse<InterviewPrepResponse>>> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;

  const body = await request.json();
  const validation = interviewPrepSchema.safeParse(body);

  if (!validation.success) {
    const error = new Error('Invalid request data') as any;
    error.statusCode = 400;
    error.details = validation.error.errors;
    throw error;
  }

  const {
    careerPath,
    experienceLevel,
    companyType,
    industryFocus,
    specificRole,
    interviewType,
    preparationTime,
    focusAreas
  } = validation.data;

  // Generate cache key
  const cacheKey = `ai:interview_prep:${userId}:${careerPath}:${companyType}_${experienceLevel}_${interviewType}`;

  // Check cache first
  const cached = await consolidatedCache.get<any>(cacheKey);
  if (cached) {
    return NextResponse.json({
      success: true,
      data: cached as InterviewPrepResponse,
      cached: true,
      message: 'Interview preparation retrieved from cache'
    });
  }

  // Perform AI analysis using optimized service
  const sessionConfig = {
    careerPath,
    experienceLevel,
    companyType,
    industryFocus: body.industryFocus,
    specificRole: body.specificRole
  };

  const prepResult = await optimizedAIService.generateInterviewQuestions(
    sessionConfig,
    {
      userId,
      enableDeduplication: true,
      enableSemanticMatch: true,
      priority: 'medium'
    }
  );

  if (!prepResult.success) {
    const error = new Error(prepResult.error || 'Failed to generate interview preparation') as any;
    error.statusCode = 500;
    throw error;
  }

  // Enhance preparation with additional structured data
  const enhancedData = {
    ...prepResult.data,
    metadata: {
      generatedAt: new Date().toISOString(),
      careerPath,
      experienceLevel,
      companyType,
      industryFocus,
      specificRole,
      interviewType,
      preparationTime,
      focusAreas
    },
    preparationPlan: generatePreparationPlan(preparationTime, focusAreas),
    companyResearch: generateCompanyResearchGuide(companyType, industryFocus),
    followUpQuestions: generateFollowUpQuestions(careerPath, experienceLevel, companyType),
    redFlags: generateRedFlags(companyType, careerPath),
    salaryNegotiation: generateSalaryGuidance(experienceLevel, careerPath, companyType)
  };

  // Cache the result for 7 days (interview prep is relatively stable)
  await consolidatedCache.set(cacheKey, enhancedData, { ttl: 7 * 24 * 60 * 60 * 1000, tags: ['interview_prep', userId] });

  // Track usage analytics
  console.log(`Interview prep generated for user ${userId}, career: ${careerPath}, company: ${companyType}`);

  return NextResponse.json({
    success: true,
    data: enhancedData,
    cached: false,
    message: 'Interview preparation generated successfully'
  });
}

function generatePreparationPlan(timeframe: string, focusAreas: string[]) {
  const plans = {
    '1_day': {
      timeline: '24 hours',
      tasks: [
        'Review job description and company basics (2 hours)',
        'Prepare STAR method examples (2 hours)',
        'Practice common questions (2 hours)',
        'Research interviewer if possible (30 minutes)',
        'Prepare questions to ask (30 minutes)',
        'Plan outfit and logistics (30 minutes)'
      ]
    },
    '3_days': {
      timeline: '3 days',
      tasks: [
        'Day 1: Deep company research and role analysis',
        'Day 2: Prepare and practice behavioral examples',
        'Day 3: Technical preparation and mock interview',
        'Daily: Review industry news and trends'
      ]
    },
    '1_week': {
      timeline: '1 week',
      tasks: [
        'Days 1-2: Comprehensive company and industry research',
        'Days 3-4: Prepare behavioral and technical examples',
        'Days 5-6: Practice with mock interviews',
        'Day 7: Final review and relaxation'
      ]
    },
    '2_weeks': {
      timeline: '2 weeks',
      tasks: [
        'Week 1: Research, skill brushup, and example preparation',
        'Week 2: Intensive practice and refinement',
        'Daily: Industry news and company updates'
      ]
    }
  };

  return plans[timeframe as keyof typeof plans] || plans['1_week'];
}

function generateCompanyResearchGuide(companyType: string, industryFocus?: string) {
  const baseResearch = [
    'Company mission, vision, and values',
    'Recent news and press releases',
    'Leadership team and company culture',
    'Products/services and target market',
    'Competitors and market position'
  ];

  const typeSpecific = {
    startup: [
      'Funding rounds and investors',
      'Growth trajectory and metrics',
      'Founding story and team',
      'Product-market fit evidence'
    ],
    corporate: [
      'Financial performance and stock trends',
      'Recent acquisitions or partnerships',
      'Corporate social responsibility initiatives',
      'Organizational structure and hierarchy'
    ],
    nonprofit: [
      'Mission impact and success stories',
      'Funding sources and financial transparency',
      'Board of directors and key stakeholders',
      'Community partnerships and programs'
    ],
    government: [
      'Department mission and public service goals',
      'Recent policy changes or initiatives',
      'Budget and resource allocation',
      'Public accountability and transparency'
    ],
    consulting: [
      'Client base and industry expertise',
      'Methodology and service offerings',
      'Case studies and success stories',
      'Partnership network and alliances'
    ],
    agency: [
      'Client portfolio and campaign successes',
      'Creative awards and recognition',
      'Team structure and collaboration style',
      'Industry specializations'
    ],
    freelance: [
      'Client testimonials and portfolio',
      'Pricing structure and value proposition',
      'Communication and project management style',
      'Availability and timeline expectations'
    ]
  };

  return {
    general: baseResearch,
    specific: typeSpecific[companyType as keyof typeof typeSpecific] || [],
    industryFocus: industryFocus ? [
      `${industryFocus} industry trends and challenges`,
      `Key players in ${industryFocus} space`,
      `Regulatory environment for ${industryFocus}`,
      `Future outlook for ${industryFocus} industry`
    ] : []
  };
}

function generateFollowUpQuestions(careerPath: string, experienceLevel: string, companyType: string) {
  const general = [
    "What does success look like in this role after 6 months?",
    "What are the biggest challenges facing the team right now?",
    "How do you measure performance and provide feedback?",
    "What opportunities are there for professional development?",
    "Can you describe the team dynamics and collaboration style?"
  ];

  const levelSpecific = {
    entry: [
      "What kind of mentorship and training is available?",
      "How do you onboard new team members?",
      "What career growth paths are typical from this role?"
    ],
    mid: [
      "What opportunities are there to lead projects or mentor others?",
      "How does this role contribute to strategic initiatives?",
      "What skills would you like to see developed in this position?"
    ],
    senior: [
      "What strategic challenges is the organization facing?",
      "How does this role influence company direction?",
      "What would you expect me to accomplish in my first 90 days?"
    ],
    executive: [
      "What is the board's vision for this role?",
      "What are the key stakeholder relationships I'd need to manage?",
      "How do you see this position evolving over the next 2-3 years?"
    ]
  };

  return {
    general,
    levelSpecific: levelSpecific[experienceLevel as keyof typeof levelSpecific] || [],
    companySpecific: companyType === 'startup' ? [
      "How do you see the company scaling over the next year?",
      "What's the runway and funding situation?"
    ] : companyType === 'corporate' ? [
      "How does this role fit into the broader organizational strategy?",
      "What are the key performance indicators for this position?"
    ] : []
  };
}

function generateRedFlags(companyType: string, careerPath: string) {
  return [
    "Vague job descriptions or responsibilities",
    "High turnover in the role or team",
    "Unwillingness to discuss compensation or benefits",
    "Poor communication during the interview process",
    "Negative reviews from current/former employees",
    "Unrealistic expectations or timeline pressures",
    "Lack of growth opportunities or career path",
    "Poor work-life balance indicators",
    "Outdated technology or processes",
    "Cultural misalignment with your values"
  ];
}

function generateSalaryGuidance(experienceLevel: string, careerPath: string, companyType: string) {
  return {
    researchTips: [
      "Use Glassdoor, PayScale, and industry reports",
      "Consider total compensation, not just base salary",
      "Factor in location and cost of living",
      "Research company-specific compensation philosophy"
    ],
    negotiationStrategy: [
      "Wait for them to make the first offer",
      "Express enthusiasm before discussing compensation",
      "Negotiate the entire package, not just salary",
      "Be prepared to justify your ask with data",
      "Consider non-monetary benefits if salary is fixed"
    ],
    timing: "Typically discussed after initial interest is established",
    companyTypeConsiderations: companyType === 'startup' ? 
      "May offer equity compensation; consider long-term potential" :
      companyType === 'nonprofit' ? 
      "Often lower base but strong benefits and mission alignment" :
      "Standard market rates with room for negotiation"
  };
}

// GET endpoint for retrieving cached prep
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 100 : 20 // Higher limit for development
    },
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const userId = session.user.id;
      const { searchParams } = new URL(request.url);
      const careerPath = searchParams.get('careerPath');
      const companyType = searchParams.get('companyType');
      const experienceLevel = searchParams.get('experienceLevel');
      const interviewType = searchParams.get('interviewType') || 'video';

      if (!careerPath || !companyType || !experienceLevel) {
        const error = new Error('Career path, company type, and experience level are required') as any;
        error.statusCode = 400;
        throw error;
      }

      const cacheKey = `ai:interview_prep:${userId}:${careerPath}:${companyType}_${experienceLevel}_${interviewType}`;
      const cached = await consolidatedCache.get<any>(cacheKey);

      if (!cached) {
        const error = new Error('Interview preparation not found') as any;
        error.statusCode = 404;
        throw error;
      }

      return NextResponse.json({
        success: true,
        data: cached,
        cached: true
      });
    }
  );
});

// POST endpoint for generating new prep
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<InterviewPrepResponse>>> => {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 30 : 6 // Higher limit for development
    },
    () => handleInterviewPrep(request)
  ) as Promise<NextResponse<ApiResponse<InterviewPrepResponse>>>;
});
