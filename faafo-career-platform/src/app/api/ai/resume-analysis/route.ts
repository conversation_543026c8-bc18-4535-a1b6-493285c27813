import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { geminiService } from '@/lib/services/geminiService';
import { consolidatedCache } from '@/lib/services/consolidated-cache-service';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { z } from 'zod';
import crypto from 'crypto';
import { withCSRFProtection } from '@/lib/csrf';
// Dynamic imports for file parsing to avoid build-time issues

// TypeScript interfaces
interface ResumeAnalysisResponse {
  data: any;
  cached?: boolean;
  message?: string;
}

interface ClearCacheResponse {
  message: string;
}

// Validation schema
const resumeAnalysisSchema = z.object({
  resumeText: z.string().min(50, 'Resume text must be at least 50 characters').max(50000, 'Resume text too long'),
  format: z.enum(['text', 'pdf', 'docx']).optional().default('text'),
});

// File upload schema
const fileUploadSchema = z.object({
  file: z.object({
    name: z.string().max(255, 'Filename too long'),
    size: z.number().max(10 * 1024 * 1024, 'File too large (max 10MB)'),
    type: z.string().refine(
      (type) => ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'].includes(type),
      'Invalid file type. Only PDF, DOCX, and TXT files are allowed'
    ),
  }),
});

async function extractTextFromFile(file: File): Promise<string> {
  const buffer = Buffer.from(await file.arrayBuffer());

  if (file.type === 'application/pdf') {
    const pdfParse = (await import('pdf-parse')).default;
    const data = await pdfParse(buffer);
    return data.text;
  } else if (file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    const mammoth = await import('mammoth');
    const result = await mammoth.extractRawText({ buffer });
    return result.value;
  } else if (file.type === 'text/plain') {
    return buffer.toString('utf-8');
  } else {
    throw new Error('Unsupported file format. Please upload PDF, DOCX, or TXT files.');
  }
}

async function handleResumeAnalysis(request: NextRequest): Promise<NextResponse<ApiResponse<ResumeAnalysisResponse>>> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;
  let resumeText: string;
  let resumeHash: string;

  const contentType = request.headers.get('content-type') || '';

  if (contentType.includes('multipart/form-data')) {
    // Handle file upload
    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      const error = new Error('No file provided') as any;
      error.statusCode = 400;
      throw error;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
      const error = new Error('File size too large. Maximum 10MB allowed.') as any;
      error.statusCode = 400;
      throw error;
    }

    // Extract text from file
    resumeText = await extractTextFromFile(file);

    if (!resumeText || resumeText.trim().length < 50) {
      const error = new Error('Could not extract sufficient text from file') as any;
      error.statusCode = 400;
      throw error;
    }
  } else {
    // Handle JSON request
    const body = await request.json();
    const validation = resumeAnalysisSchema.safeParse(body);

    if (!validation.success) {
      const error = new Error('Invalid request data') as any;
      error.statusCode = 400;
      error.details = validation.error.errors;
      throw error;
    }

    resumeText = validation.data.resumeText;
  }

  // Generate hash for caching
  resumeHash = crypto.createHash('sha256').update(resumeText).digest('hex').slice(0, 16);

  // Check cache first
  const cacheKey = `ai:resume_analysis:${userId}:${resumeHash}`;
  const cached = await consolidatedCache.get<any>(cacheKey);

  if (cached) {
    return NextResponse.json({
      success: true,
      data: cached as ResumeAnalysisResponse,
      cached: true,
      message: 'Resume analysis retrieved from cache'
    });
  }

  // Perform AI analysis
  const analysisResult = await geminiService.analyzeResume(resumeText, userId);

  if (!analysisResult.success) {
    const error = new Error(analysisResult.error || 'Failed to analyze resume') as any;
    error.statusCode = 500;
    throw error;
  }

  // Cache the result for 24 hours
  await consolidatedCache.set(cacheKey, analysisResult.data, { ttl: 24 * 60 * 60 * 1000, tags: ['resume_analysis', userId] });

  // Track usage analytics
  console.log(`Resume analysis completed for user ${userId}, hash: ${resumeHash}`);

  return NextResponse.json({
    success: true,
    data: analysisResult.data,
    cached: false,
    message: 'Resume analysis completed successfully'
  });
}

// GET endpoint for retrieving cached analysis
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ResumeAnalysisResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 20 }, // 20 requests per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const { searchParams } = new URL(request.url);
      const resumeHash = searchParams.get('hash');

      if (!resumeHash) {
        const error = new Error('Resume hash required') as any;
        error.statusCode = 400;
        throw error;
      }

      const userId = session.user.id;
      const cacheKey = `ai:resume_analysis:${userId}:${resumeHash}`;
      const cached = await consolidatedCache.get<any>(cacheKey);

      if (!cached) {
        const error = new Error('Analysis not found') as any;
        error.statusCode = 404;
        throw error;
      }

      return NextResponse.json({
        success: true,
        data: cached as ResumeAnalysisResponse,
        cached: true
      });
    }
  ) as Promise<NextResponse<ApiResponse<ResumeAnalysisResponse>>>;
});

// POST endpoint for new analysis
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ResumeAnalysisResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 analyses per 15 minutes
    () => handleResumeAnalysis(request)
  ) as Promise<NextResponse<ApiResponse<ResumeAnalysisResponse>>>;
});

// DELETE endpoint for clearing cached analysis
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ClearCacheResponse>>> => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 10 }, // 10 deletions per 15 minutes
    async () => {
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        const error = new Error('Authentication required') as any;
        error.statusCode = 401;
        throw error;
      }

      const { searchParams } = new URL(request.url);
      const resumeHash = searchParams.get('hash');

      if (!resumeHash) {
        const error = new Error('Resume hash required') as any;
        error.statusCode = 400;
        throw error;
      }

      const userId = session.user.id;
      const cacheKey = `ai:resume_analysis:${userId}:${resumeHash}`;
      await consolidatedCache.delete(cacheKey);

      return NextResponse.json({
        success: true,
        data: { message: 'Analysis cache cleared' }
      });
    }
  ) as Promise<NextResponse<ApiResponse<ClearCacheResponse>>>;
});
