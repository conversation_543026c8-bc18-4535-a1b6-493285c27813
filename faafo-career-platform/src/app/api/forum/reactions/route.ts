import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
interface ReactionsResponse {
  reactions: any[];
  counts: Record<string, number>;
  total: number;
}

interface ReactionResponse {
  id: string;
  type: string;
  userId: string;
  postId?: string;
  replyId?: string;
  createdAt: Date;
  updatedAt: Date;
  user: any;
}

interface DeleteReactionResponse {
  message: string;
}

// GET handler to retrieve reactions for a post or reply
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url);
  const postId = searchParams.get('postId');
  const replyId = searchParams.get('replyId');

  if (!postId && !replyId) {
    const error = new Error('Either postId or replyId is required') as any;
    error.statusCode = 400;
    throw error;
  }

    let reactions: any[] = [];

    if (postId) {
      reactions = await prisma.forumPostReaction.findMany({
        where: { postId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    } else if (replyId) {
      reactions = await prisma.forumReplyReaction.findMany({
        where: { replyId },
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });
    }

    // Group reactions by type for easy counting
    const reactionCounts = reactions.reduce((acc, reaction) => {
      acc[reaction.type] = (acc[reaction.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

  return NextResponse.json({
    success: true,
    data: {
      reactions,
      counts: reactionCounts,
      total: reactions.length,
    }
  });
});

// POST handler to add or update a reaction
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json();
        const { postId, replyId, type } = body;

        if (!postId && !replyId) {
          const error = new Error('Either postId or replyId is required') as any;
          error.statusCode = 400;
          throw error;
        }

        if (!type) {
          const error = new Error('Reaction type is required') as any;
          error.statusCode = 400;
          throw error;
        }

    // Validate reaction type
    const validTypes = ['LIKE', 'DISLIKE', 'HELPFUL', 'INSIGHTFUL', 'INSPIRING'];
    if (!validTypes.includes(type)) {
      return NextResponse.json(
        { error: 'Invalid reaction type' },
        { status: 400 }
      );
    }

    // Check if post or reply exists
    if (postId) {
      const post = await prisma.forumPost.findUnique({
        where: { id: postId },
      });
      if (!post) {
        return NextResponse.json({ error: 'Post not found' }, { status: 404 });
      }
    }

    if (replyId) {
      const reply = await prisma.forumReply.findUnique({
        where: { id: replyId },
      });
      if (!reply) {
        return NextResponse.json({ error: 'Reply not found' }, { status: 404 });
      }
    }

    // Use transaction with proper upsert logic to prevent race conditions
    const userId = session.user.id!; // We already checked this exists above

    // Use Prisma's built-in upsert functionality to handle race conditions
    const reaction = await prisma.$transaction(async (tx) => {
      if (postId) {
        // Handle post reactions with atomic upsert
        const existingReaction = await tx.forumPostReaction.findUnique({
          where: {
            userId_postId: {
              userId,
              postId,
            },
          },
        });

        if (existingReaction) {
          if (existingReaction.type === type) {
            // Same reaction - remove it (toggle off)
            await tx.forumPostReaction.delete({
              where: { id: existingReaction.id },
            });
            return { removed: true, message: 'Reaction removed' };
          } else {
            // Different reaction - update it atomically
            return await tx.forumPostReaction.update({
              where: { id: existingReaction.id },
              data: { type },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            });
          }
        } else {
          // New reaction - create it
          return await tx.forumPostReaction.create({
            data: {
              userId,
              type,
              postId,
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          });
        }
      } else if (replyId) {
        // Handle reply reactions with atomic upsert
        const existingReaction = await tx.forumReplyReaction.findUnique({
          where: {
            userId_replyId: {
              userId,
              replyId,
            },
          },
        });

        if (existingReaction) {
          if (existingReaction.type === type) {
            // Same reaction - remove it (toggle off)
            await tx.forumReplyReaction.delete({
              where: { id: existingReaction.id },
            });
            return { removed: true, message: 'Reaction removed' };
          } else {
            // Different reaction - update it atomically
            return await tx.forumReplyReaction.update({
              where: { id: existingReaction.id },
              data: { type },
              include: {
                user: {
                  select: {
                    id: true,
                    name: true,
                    email: true,
                  },
                },
              },
            });
          }
        } else {
          // New reaction - create it
          return await tx.forumReplyReaction.create({
            data: {
              userId,
              type,
              replyId,
            },
            include: {
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          });
        }
      }
    });

    // Handle removal case
    if (reaction && 'removed' in reaction) {
      return NextResponse.json(reaction);
    }

        return NextResponse.json({
          success: true,
          data: reaction
        });
      }
    );
  });
});

// DELETE handler to remove a reaction
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const { searchParams } = new URL(request.url);
        const postId = searchParams.get('postId');
        const replyId = searchParams.get('replyId');

        if (!postId && !replyId) {
          const error = new Error('Either postId or replyId is required') as any;
          error.statusCode = 400;
          throw error;
        }

    let reaction: any = null;

    if (postId) {
      reaction = await prisma.forumPostReaction.findFirst({
        where: {
          userId: session.user.id,
          postId,
        },
      });

      if (reaction) {
        await prisma.forumPostReaction.delete({
          where: { id: reaction.id },
        });
      }
    } else if (replyId) {
      reaction = await prisma.forumReplyReaction.findFirst({
        where: {
          userId: session.user.id,
          replyId,
        },
      });

      if (reaction) {
        await prisma.forumReplyReaction.delete({
          where: { id: reaction.id },
        });
      }
    }

        if (!reaction) {
          const error = new Error('Reaction not found') as any;
          error.statusCode = 404;
          throw error;
        }

        return NextResponse.json({
          success: true,
          data: { message: 'Reaction removed successfully' }
        });
      }
    );
  });
});
