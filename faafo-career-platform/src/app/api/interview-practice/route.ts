import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { UnifiedAuthenticationService } from '@/lib/unified-authentication-service';
import { UnifiedValidationService } from '@/lib/unified-validation-service';
import { z } from 'zod';

// Using unified validation service - no need for duplicate schema

// GET - Retrieve user's interview sessions
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withRateLimit(
    request,
    {
      windowMs: 15 * 60 * 1000,
      maxRequests: process.env.NODE_ENV === 'development' ? 100 : 30 // Higher limit for development
    },
    async () => {
      // Enhanced user validation with automatic repair using unified service
      const validation = await UnifiedAuthenticationService.validateSession(request, {
        validateUserExists: true,
        checkAccountLock: true,
        refreshSession: true,
        enableSecurityLogging: true
      });

      if (!validation.isValid) {
        const error = new Error(validation.error);
        (error as any).statusCode = validation.statusCode || 401;
        throw error;
      }

      const userId = validation.userId!;
      const { searchParams } = new URL(request.url);
      const status = searchParams.get('status');
      const limit = parseInt(searchParams.get('limit') || '10');
      const offset = parseInt(searchParams.get('offset') || '0');

      const whereClause: any = { userId };
      if (status) {
        whereClause.status = status;
      }

      const [sessions, total] = await Promise.all([
        prisma.interviewSession.findMany({
          where: whereClause,
          include: {
            questions: {
              select: {
                id: true,
                questionType: true,
                category: true,
                difficulty: true,
                questionOrder: true,
              },
              orderBy: { questionOrder: 'asc' }
            },
            responses: {
              where: { userId }, // Filter responses by current user
              select: {
                id: true,
                questionId: true,
                isCompleted: true,
                aiScore: true,
              },
            },
            _count: {
              select: {
                questions: true,
                responses: {
                  where: { userId, isCompleted: true }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          take: limit,
          skip: offset,
        }),
        prisma.interviewSession.count({ where: whereClause }),
      ]);

      const response = NextResponse.json({
        success: true,
        data: {
          sessions,
          pagination: {
            total,
            limit,
            offset,
            hasMore: offset + limit < total,
          },
        },
      });

      // Add caching headers for better performance
      response.headers.set('Cache-Control', 'private, max-age=60, stale-while-revalidate=300');

      return response;
    }
  );
});

// POST - Create new interview session
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      {
        windowMs: 15 * 60 * 1000,
        maxRequests: process.env.NODE_ENV === 'development' ? 50 : 10 // Higher limit for development
      },
      async () => {
        // Enhanced user validation with automatic repair using unified service
        const validation = await UnifiedAuthenticationService.validateSession(request, {
          validateUserExists: true,
          checkAccountLock: true,
          refreshSession: true,
          enableSecurityLogging: true
        });

        if (!validation.isValid) {
          const error = new Error(validation.error);
          (error as any).statusCode = validation.statusCode || 401;
          (error as any).securityFlags = validation.securityFlags;
          throw error;
        }

        const userId = validation.userId!;

        const body = await request.json();
        const configValidation = UnifiedValidationService.validateSessionConfig(body);

        if (!configValidation.isValid) {
          const error = new Error('Invalid request data');
          (error as any).statusCode = 400;
          (error as any).details = configValidation.errors;
          (error as any).securityFlags = configValidation.securityFlags;
          throw error;
        }

        const sessionData = configValidation.sanitizedData!;

        // Create the interview session
        const interviewSession = await prisma.interviewSession.create({
          data: {
            userId,
            sessionType: sessionData.sessionType as any,
            careerPath: sessionData.careerPath,
            experienceLevel: sessionData.experienceLevel as any,
            companyType: sessionData.companyType,
            industryFocus: sessionData.industryFocus,
            specificRole: sessionData.specificRole,
            interviewType: sessionData.interviewType as any,
            preparationTime: sessionData.preparationTime,
            focusAreas: sessionData.focusAreas,
            difficulty: sessionData.difficulty as any,
            totalQuestions: sessionData.totalQuestions,
            status: 'IN_PROGRESS',
            sessionConfig: {
              createdVia: 'api',
              userAgent: request.headers.get('user-agent'),
            },
          },
          select: {
            id: true,
            userId: true,
            sessionType: true,
            careerPath: true,
            experienceLevel: true,
            companyType: true,
            industryFocus: true,
            specificRole: true,
            interviewType: true,
            preparationTime: true,
            focusAreas: true,
            difficulty: true,
            totalQuestions: true,
            status: true,
            timeSpent: true,
            startedAt: true,
            createdAt: true,
            updatedAt: true,
            _count: {
              select: {
                questions: true,
                responses: true
              }
            }
          },
        });

        // Note: Questions will be generated when the user starts the session
        // This prevents session creation from failing due to question generation issues

        return NextResponse.json({
          success: true,
          data: interviewSession,
          message: 'Interview session created successfully',
        });
      }
    );
  });
});
