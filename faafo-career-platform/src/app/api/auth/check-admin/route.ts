import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { isUserAdmin } from '@/lib/auth-utils';
import { rateLimiters } from '@/lib/rate-limit';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

interface AdminCheckResponse {
  isAdmin: boolean;
  userId: string;
  checkedAt: string;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AdminCheckResponse>>> => {
  // Apply rate limiting
  const rateLimitResult = rateLimiters.auth.check(request);

  if (!rateLimitResult.allowed) {
    const error = new Error('Too many admin check requests') as any;
    error.statusCode = 429;
    error.headers = {
      'X-RateLimit-Limit': rateLimitResult.limit.toString(),
      'X-RateLimit-Remaining': rateLimitResult.remaining.toString(),
      'X-RateLimit-Reset': new Date(rateLimitResult.resetTime).toISOString(),
      'Retry-After': Math.ceil((rateLimitResult.resetTime - Date.now()) / 1000).toString()
    };
    throw error;
  }

  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    throw new Error('Authentication required');
  }

  // Get userId from query params
  const { searchParams } = new URL(request.url);
  const userId = searchParams.get('userId');

  // Security check: users can only check their own admin status
  // unless they are already an admin
  if (userId && userId !== session.user.id) {
    const currentUserIsAdmin = await isUserAdmin(session.user.id);
    if (!currentUserIsAdmin) {
      const error = new Error('Unauthorized to check other users admin status') as any;
      error.statusCode = 403;
      throw error;
    }
  }

  // Use current user's ID if no userId provided
  const targetUserId = userId || session.user.id;

  // Check admin status using the centralized function
  const isAdmin = await isUserAdmin(targetUserId);

  return NextResponse.json({
    success: true,
    data: {
      isAdmin,
      userId: targetUserId,
      checkedAt: new Date().toISOString()
    }
  });
});
