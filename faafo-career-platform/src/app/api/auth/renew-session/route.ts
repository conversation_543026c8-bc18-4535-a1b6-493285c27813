/**
 * Session Renewal API Endpoint
 * Handles session renewal requests
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { log } from '@/lib/logger';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface SessionRenewalResponse {
  success: boolean;
  message: string;
  expiresAt: number;
  extensionMinutes: number;
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<SessionRenewalResponse>>> => {
  // Get current session
  const session = await getServerSession(authOptions);

  if (!session) {
    log.warn('Session renewal attempted without valid session', {
      component: 'session_renewal_api',
      action: 'renewal_attempt_no_session',
      metadata: {
        userAgent: request.headers.get('user-agent'),
        ip: request.headers.get('x-forwarded-for') || 'unknown'
      }
    });

    const error = new Error('No valid session found') as any;
    error.statusCode = 401;
    throw error;
  }

  // Check if session is close to expiry
  const now = Date.now();
  const sessionExpiry = session.expires ? new Date(session.expires).getTime() : now + (30 * 60 * 1000);
  const timeUntilExpiry = sessionExpiry - now;

  // Only allow renewal if session is within 10 minutes of expiry
  const renewalWindow = 10 * 60 * 1000; // 10 minutes
  if (timeUntilExpiry > renewalWindow) {
    log.info('Session renewal attempted too early', {
      component: 'session_renewal_api',
      action: 'renewal_too_early',
      metadata: {
        userId: session.user?.id,
        timeUntilExpiry: Math.round(timeUntilExpiry / 1000),
        renewalWindow: Math.round(renewalWindow / 1000)
      }
    });

    const error = new Error('Session renewal not needed yet') as any;
    error.statusCode = 400;
    error.details = { timeUntilExpiry: Math.round(timeUntilExpiry / 1000) };
    throw error;
  }

  // Calculate new expiry time (extend by 30 minutes)
  const extensionTime = 30 * 60 * 1000; // 30 minutes
  const newExpiry = now + extensionTime;

  // In a real implementation, you would:
  // 1. Update the session in your database
  // 2. Update any JWT tokens
  // 3. Set new cookies with extended expiry

  // For NextAuth.js, the session is automatically managed
  // We'll return the new expiry time for the client to track

  log.info('Session renewed successfully', {
    component: 'session_renewal_api',
    action: 'session_renewed',
    metadata: {
      userId: session.user?.id,
      oldExpiry: sessionExpiry,
      newExpiry: newExpiry,
      extensionMinutes: extensionTime / (60 * 1000)
    }
  });

  // Set response headers to extend session cookies if needed
  const response = NextResponse.json({
    success: true as const,
    data: {
      success: true,
      message: 'Session renewed successfully',
      expiresAt: newExpiry,
      extensionMinutes: extensionTime / (60 * 1000)
    }
  });

  // Update session cookie expiry
  const cookieOptions = {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax' as const,
    maxAge: extensionTime / 1000, // Convert to seconds
    path: '/'
  };

  // Note: In a real implementation with custom session management,
  // you would update the actual session cookie here
  // For NextAuth.js, this is handled automatically

  return response;
});

interface SessionStatusResponse {
  valid: boolean;
  expiresAt: number;
  timeUntilExpiry: number;
  canRenew: boolean;
  user: {
    id: string | null | undefined;
    email: string | null | undefined;
    name: string | null | undefined;
  };
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<SessionStatusResponse>>> => {
  // Get current session status
  const session = await getServerSession(authOptions);

  if (!session) {
    const error = new Error('No valid session found') as any;
    error.statusCode = 401;
    throw error;
  }

  const now = Date.now();
  const sessionExpiry = session.expires ? new Date(session.expires).getTime() : now + (30 * 60 * 1000);
  const timeUntilExpiry = sessionExpiry - now;

  return NextResponse.json({
    success: true,
    data: {
      valid: true,
      expiresAt: sessionExpiry,
      timeUntilExpiry: Math.round(timeUntilExpiry / 1000),
      canRenew: timeUntilExpiry <= (10 * 60 * 1000), // Can renew within 10 minutes
      user: {
        id: session.user?.id,
        email: session.user?.email,
        name: session.user?.name
      }
    }
  });
});

// Handle OPTIONS for CORS
export const OPTIONS = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse> => {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
});
